[package]
name = "aoska"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "aoska"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
anyhow = "1.0.98"
reqwest = { version = "0.12.22", features = ["json"] }
chrono = {version = "0.4.41", features = ["serde"] }
strum = { version = "0.27.1", features = ["derive"] }
strum_macros = "0.27.1"
oma-pm = { version = "0.53.0", features = ["aosc"] }
oma-utils = "0.11.1"
oma-fetch = "0.31.0"
oma-tum = "0.1.0"

