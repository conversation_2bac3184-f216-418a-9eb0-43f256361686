use anyhow::{Result};
use oma_pm::{
    apt::{OmaApt, OmaAptError, OmaOperation},
    sort::SummarySort,
};
use oma_tum::{
    get_matches_tum, get_tum,
};

pub async fn check_upgradable(apt: &OmaApt) -> Result<usize, OmaAptError> {
    apt.count_pending_upgradable_pkgs()
}

pub async fn check_upgradable_detail(apt: &OmaApt) -> Result<OmaOperation, OmaAptError> {
    apt.summary(
        SummarySort::default().operation().names(),
        |_| false,
        |_| false,
    )
}

pub async fn check_security_upgradable(apt: &OmaApt) -> Result<usize> {
    let sysroot = std::path::Path::new("/");
    let tum_manifests = get_tum(sysroot)?;

    let operation = check_upgradable_detail(apt).await?;
    let matched_manifests = get_matches_tum(&tum_manifests, &operation);

    let security_count = matched_manifests
        .iter()
        .filter(|(_, entry_ref)| entry_ref.is_security())
        .count();

    Ok(security_count)
}

pub async fn check_security_upgradable_detail(apt: &OmaApt)
